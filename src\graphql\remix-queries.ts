import { gql } from "@apollo/client";

/**
 * GraphQL Queries for Remix Flow
 * These queries are specifically designed for the remix contest functionality
 * and use the remix GraphQL endpoint: https://lt7t8xl8ua.execute-api.us-east-2.amazonaws.com/prod/graphql
 */

// Query to get user information by email
export const GET_USERS_QUERY = gql`
  query Users($email: String!) {
    users(where: { email: $email }) {
      email
      name
      artistName
      phoneNumber
      profileImage
      bio
      location
      socialLinks
      isOnboarded
      createdAt
      updatedAt
    }
  }
`;

// Query to get all users (for admin purposes)
export const GET_ALL_USERS_QUERY = gql`
  query AllUsers($first: Int, $after: String) {
    usersConnection(first: $first, after: $after) {
      totalCount
      edges {
        cursor
        node {
          email
          name
          artistName
          phoneNumber
          profileImage
          bio
          location
          socialLinks
          isOnboarded
          createdAt
          updatedAt
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;

// Query to get user by ID
export const GET_USER_BY_ID_QUERY = gql`
  query UserById($id: ID!) {
    user(where: { id: $id }) {
      id
      email
      name
      artistName
      phoneNumber
      profileImage
      bio
      location
      socialLinks
      isOnboarded
      createdAt
      updatedAt
    }
  }
`;

// Query to check if user exists by email
export const CHECK_USER_EXISTS_QUERY = gql`
  query CheckUserExists($email: String!) {
    users(where: { email: $email }) {
      email
      isOnboarded
    }
  }
`;

// Mutation to create a new user
export const CREATE_USER_MUTATION = gql`
  mutation CreateUser($input: UserCreateInput!) {
    createUser(input: $input) {
      email
      name
      artistName
      phoneNumber
      profileImage
      bio
      location
      socialLinks
      isOnboarded
      createdAt
      updatedAt
    }
  }
`;

// Mutation to update user information
export const UPDATE_USER_MUTATION = gql`
  mutation UpdateUser($where: UserWhere!, $update: UserUpdateInput!) {
    updateUsers(where: $where, update: $update) {
      users {
        email
        name
        artistName
        phoneNumber
        profileImage
        bio
        location
        socialLinks
        isOnboarded
        updatedAt
      }
    }
  }
`;

// Mutation to mark user as onboarded
export const MARK_USER_ONBOARDED_MUTATION = gql`
  mutation MarkUserOnboarded($email: String!) {
    updateUsers(where: { email: $email }, update: { isOnboarded: true }) {
      users {
        email
        isOnboarded
        updatedAt
      }
    }
  }
`;

// Query for remix submissions (if applicable)
export const GET_REMIX_SUBMISSIONS_QUERY = gql`
  query RemixSubmissions($userEmail: String, $first: Int, $after: String) {
    remixSubmissions(where: { userEmail: $userEmail }, first: $first, after: $after) {
      totalCount
      edges {
        cursor
        node {
          id
          userEmail
          trackTitle
          trackUrl
          submissionDate
          status
          metadata
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;

// Mutation to submit a remix
export const SUBMIT_REMIX_MUTATION = gql`
  mutation SubmitRemix($input: RemixSubmissionCreateInput!) {
    createRemixSubmission(input: $input) {
      id
      userEmail
      trackTitle
      trackUrl
      submissionDate
      status
      metadata
    }
  }
`;

// Query to get contest information
export const GET_CONTEST_INFO_QUERY = gql`
  query ContestInfo {
    contestInfo {
      id
      title
      description
      startDate
      endDate
      isActive
      rules
      prizes
      submissionCount
    }
  }
`;

// Query for user profile with submissions
export const GET_USER_PROFILE_WITH_SUBMISSIONS_QUERY = gql`
  query UserProfileWithSubmissions($email: String!) {
    users(where: { email: $email }) {
      email
      name
      artistName
      phoneNumber
      profileImage
      bio
      location
      socialLinks
      isOnboarded
      createdAt
      updatedAt
      remixSubmissions {
        id
        trackTitle
        trackUrl
        submissionDate
        status
        metadata
      }
    }
  }
`;

// Query to search users
export const SEARCH_USERS_QUERY = gql`
  query SearchUsers($searchTerm: String!, $first: Int, $after: String) {
    usersConnection(
      where: {
        OR: [
          { name_CONTAINS: $searchTerm }
          { artistName_CONTAINS: $searchTerm }
          { email_CONTAINS: $searchTerm }
        ]
      }
      first: $first
      after: $after
    ) {
      totalCount
      edges {
        cursor
        node {
          email
          name
          artistName
          profileImage
          bio
          location
          isOnboarded
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;

// Query to get user statistics
export const GET_USER_STATS_QUERY = gql`
  query UserStats($email: String!) {
    users(where: { email: $email }) {
      email
      name
      artistName
      createdAt
      remixSubmissions {
        id
        status
        submissionDate
      }
    }
  }
`;
