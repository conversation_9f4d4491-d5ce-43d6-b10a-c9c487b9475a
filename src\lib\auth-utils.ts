import {
  signIn,
  signOut,
  signUp,
  confirmSignUp,
  resendSignUpCode,
  getCurrentUser,
  resetPassword as amplifyResetPassword,
  confirmResetPassword,
  fetchAuthSession
} from 'aws-amplify/auth';

import type {
  AuthUser,
  AuthResponse,
  LoginResponse,
  SignupResponse,
  VerificationResponse
} from '@/types/auth';

// Re-export types for convenience
export type {
  AuthUser,
  AuthResponse,
  LoginResponse,
  SignupResponse,
  VerificationResponse
};



export async function loginUser({
  email,
  password
}: {
  email: string
  password: string
}): Promise<LoginResponse> {
  try {
    const { isSignedIn, nextStep } = await signIn({
      username: email.toLowerCase().trim(),
      password: password,
    });

    if (nextStep?.signInStep) {
      switch (nextStep.signInStep) {
        case 'DONE':
          break
        case 'CONFIRM_SIGN_UP':
          const userEmail = email.toLowerCase().trim()

          try {
            await resendSignUpCode({ username: userEmail })

            return {
              success: false,
              requiresVerification: true,
              email: userEmail,
              error: 'Please verify your email address to complete sign in. A verification code has been sent to your email.'
            }
          } catch (resendError) {
            console.error('Failed to send verification code during login:', resendError)

            return {
              success: false,
              requiresVerification: true,
              email: userEmail,
              error: 'Please verify your email address to complete sign in. You can request a verification code on the next screen.'
            }
          }
        case 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED':
          return {
            success: false,
            error: 'Please change your password before continuing'
          }
        case 'CONFIRM_SIGN_IN_WITH_SMS_CODE':
        case 'CONFIRM_SIGN_IN_WITH_TOTP_CODE':
          return {
            success: false,
            error: 'Please complete MFA verification'
          }
        default:
          return {
            success: false,
            error: `Authentication challenge required: ${nextStep.signInStep}`
          }
      }
    }

    if (isSignedIn || nextStep?.signInStep === 'DONE') {
      const user = await getCurrentUser()
      return {
        success: true,
        user: {
          userId: user.userId,
          username: user.username,
          email: user.signInDetails?.loginId || email
        }
      }
    }

    return {
      success: false,
      error: 'Sign in was not completed'
    }
  } catch (error: unknown) {
    const err = error as Error & { name?: string; __type?: string; Message?: string };
    const errorName = err?.name || err?.__type || 'Unknown';
    const errorMessage = err?.message || err?.Message || 'Unknown error';

    switch (errorName) {
      case 'UserNotConfirmedException':
        return {
          success: false,
          error: 'Please verify your email address. Check your email for a confirmation link.'
        }
      case 'NotAuthorizedException':
        if (errorMessage.includes('AUTH_FLOW_NOT_SUPPORTED')) {
          return {
            success: false,
            error: 'Authentication flow not supported. Please check your Cognito User Pool app client settings.'
          }
        }
        return {
          success: false,
          error: 'Incorrect email or password. Please check your credentials and try again.'
        }
      case 'UserNotFoundException':
        return {
          success: false,
          error: 'No account found with this email address. Please check your email or sign up.'
        }
      case 'InvalidParameterException':
        return {
          success: false,
          error: 'Invalid login parameters. Please check your email format.'
        }
      case 'TooManyRequestsException':
        return {
          success: false,
          error: 'Too many login attempts. Please wait a moment and try again.'
        }
      default:
        return {
          success: false,
          error: `Authentication failed: ${errorMessage}`
        }
    }
  }
}

// Signup function
export async function signupUser({
  email,
  password,
  username
}: {
  email: string
  password: string
  username?: string
}): Promise<SignupResponse> {
  try {
    // Start with minimal required attributes
    const userAttributes: Record<string, string> = {
      email: email.toLowerCase().trim()
    };

    // Add optional attributes if provided
    if (username) {
      userAttributes.name = username;
      userAttributes.preferred_username = username;
    }

    const signUpParams = {
      username: email.toLowerCase().trim(), // Use email as username
      password,
      options: {
        userAttributes
      }
    };



    const result = await signUp(signUpParams)

    return {
      success: true,
      userId: result.userId,
      nextStep: result.nextStep ? {
        signUpStep: result.nextStep.signUpStep,
        codeDeliveryDetails: 'codeDeliveryDetails' in result.nextStep
          ? result.nextStep.codeDeliveryDetails
          : undefined
      } : undefined
    }
  } catch (error) {
    // Handle specific Cognito error messages
    if (error instanceof Error) {

      switch (error.name) {
        case 'UsernameExistsException':
          return {
            success: false,
            error: 'An account with this email already exists. Please try signing in instead.'
          }
        case 'InvalidPasswordException':
          return {
            success: false,
            error: 'Password does not meet requirements. Please ensure it has at least 8 characters.'
          }
        case 'InvalidParameterException':
          return {
            success: false,
            error: `Invalid parameter: ${error.message}`
          }
        default:
          return {
            success: false,
            error: `Signup failed: ${error.message}`
          }
      }
    }

    return {
      success: false,
      error: 'An unexpected error occurred during signup'
    }
  }
}

// Email verification function
export async function verifyEmail({
  email,
  code
}: {
  email: string
  code: string
}): Promise<VerificationResponse> {
  try {
    const result = await confirmSignUp({
      username: email,
      confirmationCode: code
    })

    if (result.isSignUpComplete) {
      // Return success without trying to get user details immediately
      // The user will need to sign in after verification
      return {
        success: true,
        user: {
          userId: '', // Will be populated after sign in
          username: email,
          email: email
        }
      }
    }

    return {
      success: false,
      error: 'Email verification was not completed'
    }
  } catch (error) {
    // Handle specific Cognito error messages
    if (error instanceof Error) {
      switch (error.name) {
        case 'CodeMismatchException':
          return {
            success: false,
            error: 'Invalid verification code. Please check the code and try again.'
          }
        case 'ExpiredCodeException':
          return {
            success: false,
            error: 'Verification code has expired. Please request a new code.'
          }
        case 'UserNotFoundException':
          return {
            success: false,
            error: 'User not found. Please sign up first.'
          }
        default:
          return {
            success: false,
            error: `Verification failed: ${error.message}`
          }
      }
    }

    return {
      success: false,
      error: 'An unexpected error occurred during verification'
    }
  }
}

// Resend verification code function
export async function resendVerificationCode(email: string): Promise<AuthResponse> {
  try { await resendSignUpCode({ username: email })
    return { success: true }
  } catch (error) {

    if (error instanceof Error) {
      switch (error.name) {
        case 'UserNotFoundException':
          return {
            success: false,
            error: 'User not found. Please sign up first.'
          }
        case 'InvalidParameterException':
          return {
            success: false,
            error: 'Invalid email address format.'
          }
        case 'TooManyRequestsException':
          return {
            success: false,
            error: 'Too many requests. Please wait before requesting another code.'
          }
        case 'LimitExceededException':
          return {
            success: false,
            error: 'Daily email limit exceeded. Please try again tomorrow.'
          }
        default:
          return {
            success: false,
            error: `Failed to send verification code: ${error.message}`
          }
      }
    }

    return {
      success: false,
      error: 'Failed to resend verification code'
    }
  }
}

export async function forgotPassword(email: string): Promise<AuthResponse> {
  try {
    await amplifyResetPassword({ username: email })
    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to initiate password reset'
    }
  }
}

export async function resetPassword(
  email: string,
  code: string,
  newPassword: string
): Promise<AuthResponse> {
  try {
    await confirmResetPassword({
      username: email,
      confirmationCode: code,
      newPassword: newPassword
    })
    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to reset password'
    }
  }
}

// Sign out function
export async function logoutUser(): Promise<AuthResponse> {
  try {
    await signOut()
    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to sign out'
    }
  }
}

// Get current authenticated user
export async function getCurrentAuthUser(): Promise<AuthUser | null> {
  try {
    const user = await getCurrentUser()
    return {
      userId: user.userId,
      username: user.username,
      email: user.signInDetails?.loginId || ''
    }
  } catch {
    return null
  }
}

// Get JWT token for authenticated requests
export async function getAuthToken(): Promise<string | null> {
  try {
    const session = await fetchAuthSession()
    const token = session.tokens?.idToken?.toString()
    return token || null
  } catch (error) {
    console.error('Failed to get auth token:', error)
    return null
  }
}